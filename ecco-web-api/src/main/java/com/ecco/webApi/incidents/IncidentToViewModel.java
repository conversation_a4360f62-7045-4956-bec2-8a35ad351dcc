package com.ecco.webApi.incidents;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.util.EccoMessageUtils;
import com.ecco.infrastructure.util.EccoTimeUtils;
import com.ecco.servicerecipient.AcceptState;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusView;
import com.ecco.dom.incidents.Incident;

import java.util.function.Function;

public class IncidentToViewModel implements Function<Incident, IncidentViewModel> {

    private final ListDefinitionRepository listDefinitionRepository;

    public IncidentToViewModel(ListDefinitionRepository listDefinitionRepository) {
        this.listDefinitionRepository = listDefinitionRepository;
    }

    @Override
    public IncidentViewModel apply(Incident input) {

        IncidentViewModel result = new IncidentViewModel();
        result.incidentId = input.getId();
        result.serviceRecipientId = input.getServiceRecipientId();
        result.serviceAllocationId = input.getServiceRecipient().getServiceAllocationId();

        result.receivedDate = input.getReceivedDateJdk();
        //result.name = input.getName();
        result.categoryId = input.getCategoryId();
        result.categoryName = input.getCategoryId() != null ? this.listDefinitionRepository.findById(input.getCategoryId()).get().getName() : null;
        result.reportedById = input.getReportedById();
        result.reportedBy = input.getReportedBy();
        result.reportedByContact = input.getReportedByContact();
        result.emergencyServicesInvolved = input.getEmergencyServicesInvolved();
        result.hospitalisationInvolved = input.getHospitalisationInvolved();

        // accept on service
        AcceptState[] states = ServiceRecipientCaseStatusView.Support.acceptedStates(input.isFinalDecision(), null, false, input.isAcceptedOnService());
        //result.appropriateReferralState = states[0];
        result.acceptOnServiceState = states[1];

        result.statusMessageKey = ServiceRecipientCaseStatusView.Support.getStatusMessageKey(input);
        result.statusMessage = EccoMessageUtils.getUiMessageSource().getMessage(result.statusMessageKey);

        result.decisionMadeOn = JodaToJDKAdapters.localDateToJDk(input.getDecisionMadeOn());
        result.signpostedReasonId = input.getSignpostedReasonId();
        result.signpostedBack = input.isSignpostedBack();
        result.signpostedAgencyId = input.getSignpostedAgencyId();
        result.signpostedExitComment = input.getSignpostedExitComment();

        // start
        if (input.getReceivingServiceDate() != null) {
            result.receivingServiceDate = input.getReceivingServiceDate().toLocalDate();
        }
        result.supportWorkerId = input.getSupportWorkerId();

        // exit
        result.exitedDate = input.getExited();
        result.exitReasonId = input.getExitReasonId();
        result.reviewDate = input.getReviewDate();

        return result;
    }

}
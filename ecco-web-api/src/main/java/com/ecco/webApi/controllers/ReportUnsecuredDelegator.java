package com.ecco.webApi.controllers;

import com.ecco.config.dom.Setting;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.config.service.SettingsService;
import com.ecco.dao.*;
import com.ecco.dao.querydsl.EntityRestrictionCommonPredicates;
import com.ecco.dao.querydsl.PredicateSupport;
import com.ecco.dom.*;
import com.ecco.dom.contacts.AddressHistory;
import com.ecco.dom.contacts.QAddressHistory;
import com.ecco.dom.groupsupport.GroupSupportActivity;
import com.ecco.dom.groupsupport.QGroupActivity_Referral;
import com.ecco.dom.groupsupport.QGroupSupportActivity;
import com.ecco.dom.incidents.Incident;
import com.ecco.dom.incidents.QIncident;
import com.ecco.dom.managedvoids.ManagedVoid;
import com.ecco.dom.managedvoids.QManagedVoid;
import com.ecco.dom.repairs.QRepair;
import com.ecco.dom.repairs.Repair;
import com.ecco.dom.servicerecipients.QBaseServiceRecipient;
import com.ecco.groupsupport.repositories.GroupSupportActivityRepository;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.spring.data.QueryModifier;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.repositories.repairs.RepairRateRepository;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.service.TaskDefinitionService;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.dom.QServiceCategorisation;
import com.ecco.serviceConfig.dom.TaskDefinition;
import com.ecco.webApi.contacts.occupancy.OccupancyHistoryToViewModel;
import com.ecco.webApi.contacts.occupancy.OccupancyHistoryViewModel;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.groupSupport.*;
import com.ecco.webApi.incidents.IncidentListController;
import com.ecco.webApi.incidents.IncidentListRowResourceAssembler;
import com.ecco.webApi.incidents.IncidentViewModel;
import com.ecco.webApi.managedvoids.ManagedVoidListController;
import com.ecco.webApi.managedvoids.ManagedVoidListRowResourceAssembler;
import com.ecco.webApi.managedvoids.ManagedVoidViewModel;
import com.ecco.webApi.repairs.RepairListController;
import com.ecco.webApi.repairs.RepairListRowResourceAssembler;
import com.ecco.webApi.repairs.RepairViewModel;
import com.ecco.webApi.viewModels.ResourceList;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.*;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.hateoas.IanaLinkRelations;

import org.jspecify.annotations.Nullable;
import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.ecco.config.service.SettingsService.Reports.PageSizeDefault;
import static com.ecco.config.service.SettingsService.Reports.PageSizeReferrals;
import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static com.querydsl.core.group.GroupBy.groupBy;
import static com.querydsl.core.group.GroupBy.list;
import static com.querydsl.core.types.ExpressionUtils.and;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;


/**
 * Shared methods between differing secured entities
 */
@ReadOnlyTransaction
@RequiredArgsConstructor
public class ReportUnsecuredDelegator extends BaseWebApiController {

    @PersistenceContext
    protected EntityManager em;

    private final ReferralSummaryToViewModel referralSummaryToViewModel;
    private final ReferralToViewModel referralToViewModel;
    private final ReferralRepository referralRepository;
    private final GroupSupportActivityRepository activityRepository;
    private final EntityRestrictionService entityRestrictionService;
    private final RepositoryBasedServiceCategorisationService serviceCategorisationService;
    private final TaskDefinitionService taskDefinitionService;
    private final ListDefinitionRepository listDefinitionRepository;
    private final RepairRateRepository repairRateRepository;
    private final IndividualRepository individualRepository;
    private final EvidenceSupportSmartStepToViewModel supportActionsToViewModel = new EvidenceSupportSmartStepToViewModel();
    private final ThreatSmartStepToViewModel threatActionsToViewModel = new ThreatSmartStepToViewModel();
    private final AnswerToSnapshotViewModel answersToSnapshotViewModel = new AnswerToSnapshotViewModel();
    private GroupSupportActivitySummaryRowResourceAssembler activityAssembler;

    @PostConstruct
    public void init() {
        this.activityAssembler = new GroupSupportActivitySummaryRowResourceAssembler(activityRepository);
    }

    /** Utility function to create using EntityManager but match {@link JPAExpressions#selectFrom(EntityPath)} which
     * is only for detached queries (e.g. subqueries).
     */
    protected <T> JPAQuery<T> query(EntityPath<T> entityPath) {
        return new JPAQuery<T>(em)
                .from(entityPath);
//                .select(entityPath); // originally just .from and our select can change according to projection
    }

    public Stream<ReferralSummaryViewModel> reportReferralSummaryViewModel(ReportCriteriaDto dto, @Nullable PageRequest pr, boolean hideRequestedDelete) {
        final Iterable<ReferralSummary> referrals = reportReferralSummary(dto, pr, hideRequestedDelete);
        return StreamSupport.stream(referrals.spliterator(), false)
                .map(referralSummaryToViewModel);
    }

    public Iterable<ReferralSummary> reportReferralSummary(ReportCriteriaDto dto, @Nullable PageRequest pr, boolean hideRequestedDelete) {
        Predicate p = hideRequestedDelete
                ? ReferralPredicates.referralPredicateHideDeleteRequested(dto, entityRestrictionService, serviceCategorisationService)
                : ReferralPredicates.referralPredicate(dto, entityRestrictionService, serviceCategorisationService);

        return pr != null
                ? referralRepository.findAllAsReferralSummary(p, pr)
                : referralRepository.findAllAsReferralSummary(p);
    }

    public Iterable<Referral> reportReferralsRaw(@Nullable Integer page, ReportCriteriaDto dto) {
        Predicate p = ReferralPredicates.referralPredicate(dto, entityRestrictionService, serviceCategorisationService);
        return page != null ? referralRepository.findAll(p, getPageSize(PageSizeReferrals).asPageRequest(page))
                : referralRepository.findAll(p);
    }

    public Iterable<ReferralViewModel> reportReferrals(@Nullable Integer page, ReportCriteriaDto dto) {

        final Iterable<Referral> referrals = reportReferralsRaw(page, dto);

        List<ReferralViewModel> viewModels = StreamSupport.stream(referrals.spliterator(), false)
                .map(referralToViewModel).collect(toList());

        addChildSrIdsIfNecessary(dto, viewModels);
        return viewModels;
    }

    public void addChildSrIdsIfNecessary(ReportCriteriaDto dto, Iterable<ReferralViewModel> viewModels) {

        // Only do so if wanted and we have some referrals
        if (dto.getOptionalData() == null || !dto.getOptionalData().contains("childRecipientIds")
                || !viewModels.iterator().hasNext()) {
            return;
        }

        Long[] ids = StreamSupport.stream(viewModels.spliterator(), false)
                .map(r -> r.referralId).toArray(Long[]::new);
        List<Tuple> childSrIds = referralRepository.findAllServiceRecipientIdByParentReferral_Ids(ids);

        if (childSrIds.size() == 0) {
            return;
        }

        Map<Long, List<Tuple>> childSridsByParentId = childSrIds.stream().collect(Collectors.groupingBy(tuple -> tuple.get(0, Long.class)));
        viewModels.forEach(rvm -> {
            List<Tuple> childIds = childSridsByParentId.get(rvm.referralId);
            if (childIds != null) {
                rvm.setChildServiceRecipientIds(childIds.stream()
                        .map(tuple -> tuple.get(1, Integer.class)).collect(toList()));
            }
        });
    }

    public List<OccupancyHistoryViewModel> occupancyHistory(Integer page, ReportCriteriaDto dto) {
        var results = addressHistory(page, dto, true);

        var convert = new OccupancyHistoryToViewModel();
        List<OccupancyHistoryViewModel> viewModels = results.stream().map(convert).collect(toList());

        // TODO ? include buildings for which there are no entries as a gap
        // TODO include the building 'start' date - it might not be a gap
        return OccupancyHistoryViewModel.fillOccupancyGaps(viewModels, dto);
    }

    protected List<AddressHistory> addressHistory(Integer page, ReportCriteriaDto dto, boolean buildingIdsRequired) {
        QAddressHistory qHistory = QAddressHistory.addressHistory;
        QBaseServiceRecipient qSr = QBaseServiceRecipient.baseServiceRecipient;

        // dates of address changes
        // we include any 'around' the dates so we are safe to fill in gaps of unoccupied
        // except that we don't know when the previous occupation was, unless we persist data or specifcally load n-1
        var dtoFrom = JodaToJDKAdapters.localDateTimeToJDk(dto.getFromDate().toLocalDateTime(LocalTime.MIDNIGHT));
        BooleanExpression pAround = qHistory.validFrom.goe(dtoFrom).or(qHistory.validTo.goe(dtoFrom));
        if (dto.getToDate() != null) {
            var dtoTo = JodaToJDKAdapters.localDateTimeToJDk(dto.getToDate().toLocalDateTime(LocalTime.MIDNIGHT));
            pAround = pAround.and(
                    qHistory.validFrom.before(dtoTo.plusDays(1))
                            .or(qHistory.validTo.before(dtoTo.plusDays(1)))
            );
        }

        // filter/status
        BooleanExpression pStatus = null;
        if (dto.getEntityStatus() != null) {

            pStatus = switch (dto.getEntityStatus()) {
                case "void" -> {
                    // Include both unmanaged voids (no serviceRecipientId) and managed voids (prefix = "mv")
                    BooleanExpression unmanagedVoid = qHistory.serviceRecipientId.isNull();
                    BooleanExpression managedVoid = qHistory.serviceRecipientId.isNotNull().and(qSr.discriminator.eq("mv"));
                    yield unmanagedVoid.or(managedVoid);
                }
                case "occupied" -> {
                    // Only include non-null serviceRecipientId that are NOT managed voids (prefix != "mv")
                    yield qHistory.serviceRecipientId.isNotNull().and(qSr.discriminator.ne("mv"));
                }
                default -> pStatus;
            };
        }

        // TODO search... on the client name perhaps (the serviceRecipientId on the history)
        //  see ReferralsListControl generateSearchConfig which matches referralCode / referralId / clientDisplayName

        // Apply security predicate only when there is a service recipient (for managed voids and occupied spaces)
        Predicate pServiceRecipient = EntityRestrictionCommonPredicates.applySecurityPredicate(qSr, dto, entityRestrictionService, serviceCategorisationService);
        BooleanExpression pServiceRecipientSecurity = qHistory.serviceRecipientId.isNull().or(pServiceRecipient);
        BooleanExpression sameSrId = qHistory.serviceRecipientId.eq(qSr.id);

        if (dto.getServiceRecipientFilter() != null) {
            sameSrId = qSr.id.eq(Integer.parseInt(StringUtils.substringAfter(dto.getServiceRecipientFilter(), ":"))).and(sameSrId);
        }

        BooleanExpression hasBuildingId = buildingIdsRequired ? qHistory.buildingLocationId.isNotNull() : null;

        BooleanExpression buildingIds = null;
        if (dto.getBuildingIds() != null) {
            buildingIds = qHistory.buildingLocationId.in(dto.getBuildingIds());
        }

        // use joins to bring the disparate predicates together
        // in query-dsl 4, we could use JPAExpressions to avoid entityManager
        // Use leftJoin to include unmanaged voids (where serviceRecipientId is null)
        JPQLQuery<AddressHistory> query = query(qHistory)
                .leftJoin(qSr).on(qHistory.serviceRecipientId.eq(qSr.id))
                .where(sameSrId, pServiceRecipientSecurity, pAround, pStatus, hasBuildingId, buildingIds)
                .orderBy(qHistory.validFrom.desc());

        if (page != null) {
            PageRequest pr = getPageSize(PageSizeDefault).asPageRequest(page);
            query.limit(pr.getPageSize()).offset(pr.getOffset());
        }
        return query.select(qHistory).fetch();
    }

    // NB we probably don't need a report - we have one around attendances
    public ResourceList<ManagedVoidViewModel> reportManagedVoidSummaries(Integer buildingId, boolean all, Long onlyMy, PageRequest pr) {

        Page<ManagedVoid> results = reportManagedVoidQuery(buildingId, all, onlyMy, pr);

        var toSummary = new ManagedVoidListRowResourceAssembler(listDefinitionRepository, individualRepository);
        Page<ManagedVoidViewModel> resourcePage = results.map(toSummary::toModel);
        ResourceList<ManagedVoidViewModel> resourceList = new ResourceList<>(resourcePage.getContent());

        if (resourcePage.hasPrevious()) {
            resourceList.add(
                    linkToApi(methodOn(ManagedVoidListController.class).list(buildingId, pr.getPageNumber() - 1, pr.getPageSize()))
                            .withRel(IanaLinkRelations.PREV));
        }
        if (resourcePage.hasNext()) {
            resourceList.add(
                    linkToApi(methodOn(ManagedVoidListController.class).list(buildingId, pr.getPageNumber() + 1, pr.getPageSize()))
                            .withRel(IanaLinkRelations.NEXT));
        }
        resourceList.setNumPages(resourcePage.getTotalPages());
        resourceList.setPageSize(pr.getPageSize());
        resourceList.setNumItems(resourcePage.getTotalElements());

        return resourceList;
    }

    private Page<ManagedVoid> reportManagedVoidQuery(Integer buildingId, boolean all, Long onlyMy, PageRequest pr) {
        QManagedVoid managedVoidQ = QManagedVoid.managedVoid;
        QServiceCategorisation svcAllocQ = QServiceCategorisation.serviceCategorisation;

        BooleanBuilder p = new BooleanBuilder();

        // our security criteria
        Predicate pServiceAllocation = EntityRestrictionCommonPredicates.applySecurityPredicate(svcAllocQ, null, entityRestrictionService, serviceCategorisationService);
        p.and(pServiceAllocation);

        // perhaps...if we are in the building, we should see all repairs if we can see the manu item
        // and if we are not, then we obey the my/all
        if (buildingId == null) {
            // see my repairs, or everything
            if (!all && onlyMy != null) {
                p.and(managedVoidQ.supportWorkerId.eq(onlyMy));
            }
        } else {
            p.and(managedVoidQ.addressHistory.buildingLocationId.eq(buildingId));
        }

        // perhaps...if we are in the building, we should see all repairs if we can see the manu item
        // and if we are not, then we obey the my/all
        //if (buildingId == null) {
            // see my repairs, or everything
            if (!all && onlyMy != null) {
                p.and(managedVoidQ.supportWorkerId.eq(onlyMy));
            }
        /*} else {
            p.and(repairQ.buildingId.eq(buildingId));
        }*/

        // we could also use the repository, if we did svcAllocQ via repairQ not join
        //final Page<Repair> repairs = repairRepository.findAll(p, pr);
        JPQLQuery<Repair> query = new JPAQuery<>(em);
        query.from(managedVoidQ)
                .join(managedVoidQ.serviceRecipient.serviceAllocation, svcAllocQ)
                .orderBy(managedVoidQ.receivedDate.desc());
        query.where(p);
        var total = query.fetchCount();

        query.limit(pr.getPageSize()).offset(pr.getOffset());
        var repairs = query.fetch();

        return new PageImpl(repairs, pr, total);
    }

    // NB we probably don't need a report - we have one around attendances
    public ResourceList<RepairViewModel> reportRepairSummaries(Integer buildingId, boolean allRepairs, Long onlyMyRepairs, PageRequest pr) {

        Page<Repair> results = reportRepairQuery(buildingId, allRepairs, onlyMyRepairs, pr);

        RepairListRowResourceAssembler toSummary = new RepairListRowResourceAssembler(listDefinitionRepository, individualRepository, repairRateRepository);
        Page<RepairViewModel> resourcePage = results.map(toSummary::toModel);
        ResourceList<RepairViewModel> resourceList = new ResourceList<>(resourcePage.getContent());

        if (resourcePage.hasPrevious()) {
            resourceList.add(
                    linkToApi(methodOn(RepairListController.class).list(buildingId,pr.getPageNumber() - 1, pr.getPageSize()))
                            .withRel(IanaLinkRelations.PREV));
        }
        if (resourcePage.hasNext()) {
            resourceList.add(
                    linkToApi(methodOn(RepairListController.class).list(buildingId,pr.getPageNumber() + 1, pr.getPageSize()))
                            .withRel(IanaLinkRelations.NEXT));
        }
        resourceList.setNumPages(resourcePage.getTotalPages());
        resourceList.setPageSize(pr.getPageSize());
        resourceList.setNumItems(resourcePage.getTotalElements());

        return resourceList;
    }

    private Page<Repair> reportRepairQuery(Integer buildingId, boolean allRepairs, Long onlyMyRepairs, PageRequest pr) {
        QRepair repairQ = QRepair.repair;
        QServiceCategorisation svcAllocQ = QServiceCategorisation.serviceCategorisation;

        BooleanBuilder p = new BooleanBuilder();

        // our security criteria
        Predicate pServiceAllocation = EntityRestrictionCommonPredicates.applySecurityPredicate(svcAllocQ, null, entityRestrictionService, serviceCategorisationService);
        p.and(pServiceAllocation);

        // perhaps...if we are in the building, we should see all repairs if we can see the manu item
        // and if we are not, then we obey the my/all
        if (buildingId == null) {
            // see my repairs, or everything
            if (!allRepairs && onlyMyRepairs != null) {
                p.and(repairQ.supportWorkerId.eq(onlyMyRepairs));
            }
        } else {
            p.and(repairQ.buildingId.eq(buildingId));
        }

        // we could also use the repository, if we did svcAllocQ via repairQ not join
        //final Page<Repair> repairs = repairRepository.findAll(p, pr);
        JPQLQuery<Repair> query = new JPAQuery<>(em);
        query.from(repairQ)
                .join(repairQ.serviceRecipient.serviceAllocation, svcAllocQ)
                .orderBy(repairQ.receivedDate.desc());
        query.where(p);
        var total = query.fetchCount();

        query.limit(pr.getPageSize()).offset(pr.getOffset());
        var repairs = query.fetch();

        return new PageImpl(repairs, pr, total);
    }

    // NB we probably don't need a report - we have one around attendances
    public ResourceList<IncidentViewModel> reportIncidentSummaries(Integer contactSrId, Long contactId, boolean allIncidents, boolean teamIncidents, Long onlyMyIncidents, PageRequest pr) {

        Page<Incident> results = reportIncidentQuery(contactId, allIncidents, teamIncidents, onlyMyIncidents, pr);

        IncidentListRowResourceAssembler toSummary = new IncidentListRowResourceAssembler(listDefinitionRepository, individualRepository);
        Page<IncidentViewModel> resourcePage = results.map(toSummary::toModel);
        ResourceList<IncidentViewModel> resourceList = new ResourceList<>(resourcePage.getContent());

        if (resourcePage.hasPrevious()) {
            resourceList.add(
                    linkToApi(methodOn(IncidentListController.class).list(contactSrId,pr.getPageNumber() - 1, pr.getPageSize()))
                            .withRel(IanaLinkRelations.PREV));
        }
        if (resourcePage.hasNext()) {
            resourceList.add(
                    linkToApi(methodOn(IncidentListController.class).list(contactSrId,pr.getPageNumber() + 1, pr.getPageSize()))
                            .withRel(IanaLinkRelations.NEXT));
        }
        resourceList.setNumPages(resourcePage.getTotalPages());
        resourceList.setPageSize(pr.getPageSize());
        resourceList.setNumItems(resourcePage.getTotalElements());

        return resourceList;
    }

    private Page<Incident> reportIncidentQuery(Long contactId, boolean allIncidents, boolean teamIncidents, Long onlyMyIncidents, PageRequest pr) {
        QIncident incidentQ = QIncident.incident;
        QServiceCategorisation svcAllocQ = QServiceCategorisation.serviceCategorisation;

        BooleanBuilder p = new BooleanBuilder();

        if (!allIncidents) {
            if (!teamIncidents && onlyMyIncidents != null) {
                p.and(incidentQ.supportWorkerId.eq(onlyMyIncidents));
            // default to team if the user doesn't have a contactId
            } else {
                Predicate pServiceAllocation = EntityRestrictionCommonPredicates.applySecurityPredicate(svcAllocQ, null, entityRestrictionService, serviceCategorisationService);
                p.and(pServiceAllocation);
            }
        }

        // any filtering
        // - we want to filter incidents to a contactId, when we are on a referral file
        // - so join with the contact's srIds - which are the correct incident files to see
        // - NB but incidents and contacts are unconnected via the entities
        // - so we just specify an in, as there won't be a troublesome number of incident contacts
        if (contactId != null) {
            var contactIncidentSrIds = new JPAQuery<>(em).from(QServiceRecipientContact.serviceRecipientContact)
                    .where(QServiceRecipientContact.serviceRecipientContact.id.contactId.eq(contactId))
                    .select(QServiceRecipientContact.serviceRecipientContact.id.serviceRecipientId)
                    .fetch();
            p.and(incidentQ.serviceRecipientId.in(contactIncidentSrIds));
        }

        // we could also use the repository, if we did svcAllocQ via incidentQ not join
        //final Page<Incident> incidents = incidentRepository.findAll(p, pr);
        JPQLQuery<Incident> query = new JPAQuery<>(em);
        query.from(incidentQ)
                .join(incidentQ.serviceRecipient.serviceAllocation, svcAllocQ)
                .orderBy(incidentQ.receivedDate.desc());
        query.where(p);
        var total = query.fetchCount();

        query.limit(pr.getPageSize()).offset(pr.getOffset());
        var incidents = query.fetch();

        return new PageImpl(incidents, pr, total);
    }

    public ResourceList<GroupSupportActivitySummaryRowResource> reportGroupAuxActivitySummaries(ReportCriteriaDto dto, PageRequest pr) {
        return reportGroupActivitySummaries(dto, pr, GroupSupportActivity.DISCRIMINATOR_AUX);
    }

    // NB we probably don't need a report - we have one around attendances
    public ResourceList<GroupSupportActivitySummaryRowResource> reportGroupCommsActivitySummaries(ReportCriteriaDto dto, PageRequest pr) {
        return reportGroupActivitySummaries(dto, pr, GroupSupportActivity.DISCRIMINATOR_COMMS);
    }

    // NB we probably don't need a report - we have one around attendances
    public ResourceList<GroupSupportActivitySummaryRowResource> reportGroupSupportActivitySummaries(ReportCriteriaDto dto, PageRequest pr) {
        return reportGroupActivitySummaries(dto, pr, GroupSupportActivity.DISCRIMINATOR_SUPPORT);
    }

    // NB we probably don't need a report - we have one around attendances
    public ResourceList<GroupSupportActivitySummaryRowResource> reportGroupActivitySummaries(ReportCriteriaDto dto, PageRequest pr, String type) {

        Page<GroupSupportActivity> results = activityQuery(dto, pr, type);

        GroupSupportActivityToViewModel toSummary = new GroupSupportActivityToViewModel();
        Page<GroupSupportActivitySummaryRowResource> resourcePage = results.map(a -> activityAssembler.toModel(toSummary.apply(a)));
        Map<Long, GroupSupportActivitySummaryStats> resultsMap = resourcePage.getContent().stream().collect(Collectors.toMap(GroupSupportActivitySummaryRowResource::getId, a -> a));
        applyTotalsToGroupSupportSummaries(resultsMap);

        ResourceList<GroupSupportActivitySummaryRowResource> resourceList = new ResourceList<>(resourcePage.getContent());

        if (resourcePage.hasPrevious()) {
            resourceList.add(
                    linkToApi(methodOn(GroupSupportActivityController.class).findActivities(dto.getParentId(), dto.getGroupPageType(), dto.getServiceId(), dto.getServiceCategorisationId(), dto.getActivityTypeId(), dto.getVenueId(), pr.getPageNumber() - 1, pr.getPageSize()))
                            .withRel(IanaLinkRelations.PREV));
        }
        if (resourcePage.hasNext()) {
            resourceList.add(
                    linkToApi(methodOn(GroupSupportActivityController.class).findActivities(dto.getParentId(), dto.getGroupPageType(), dto.getServiceId(), dto.getServiceCategorisationId(), dto.getActivityTypeId(), dto.getVenueId(), pr.getPageNumber() - 1, pr.getPageSize()))
                            .withRel(IanaLinkRelations.NEXT));
        }
        resourceList.setNumPages(resourcePage.getTotalPages());
        resourceList.setPageSize(pr.getPageSize());
        resourceList.setNumItems(resourcePage.getTotalElements());

        return resourceList;
    }

    public void applyTotalsToGroupSupportSummaries(Map<Long, GroupSupportActivitySummaryStats> resultsMap) {
        // get all the counts in one query
        // NB to do one query for the whole lot, look at previous attempts on c1f262c, and below 'findGroupedWorkAnalysis'
        // NB error when using path through multiId 'qR.multiId.activity' perhaps when using groupBy
        // because below 'activityAttendanceQuery' works fine.
        // we get: org.hibernate.QueryException: could not resolve property: multiId.activity
        QGroupActivity_Referral qR = QGroupActivity_Referral.groupActivity_Referral;
        NumberExpression<Integer> attendingExp = new CaseBuilder()
                .when(qR.attending.isTrue())
                .then(Expressions.ONE).otherwise(Expressions.ZERO).sum();
        NumberExpression<Integer> attendedExp = new CaseBuilder()
                .when(qR.attended.isTrue())
                .then(Expressions.ONE).otherwise(Expressions.ZERO).sum();
        NumberExpression<Long> totalRecordsExp = qR.count();
        JPQLQuery<Tuple> groupBy = new JPAQuery<Tuple>(em)
                .select(Projections.tuple(qR.activityId, totalRecordsExp, attendingExp, attendedExp))
                .from(qR)
                .where(qR.activityId.in(resultsMap.keySet()))
                .groupBy(qR.activityId);
        List<Tuple> counts = groupBy
                .fetch();

        // merge the results
        counts.stream().forEach(tup -> {
            var as = resultsMap.get(tup.get(qR.activityId));
            as.setClientsInvited(tup.get(1, Long.class).intValue()); // invited is all the records
            as.setClientsAttending(tup.get(2, Integer.class));
            as.setClientsAttended(tup.get(3, Integer.class));
        });

        // specifically set zeros to avoid null errors on client
        // NB retaining the use of Integer ensures we get client errors rather than mistaken data
        Set<Long> foundIds = counts.stream().map(item -> item.get(qR.activityId)).collect(toSet());
        Set<Long> notFoundIds = new HashSet<>(resultsMap.keySet());
        notFoundIds.removeAll(foundIds);
        notFoundIds.forEach(id -> {
            var summary = resultsMap.get(id);
            summary.setClientsInvited(0);
            summary.setClientsAttending(0);
            summary.setClientsAttended(0);
        });
    }

    private Page<GroupSupportActivity> activityQuery(ReportCriteriaDto dto, PageRequest pr, String type) {
        log.info("Running report for activity criteria " + dto.toString());

        QGroupSupportActivity groupActivity = QGroupSupportActivity.groupSupportActivity;
        QServiceCategorisation svcAllocQ = QServiceCategorisation.serviceCategorisation;

        // our security criteria
        Predicate pServiceAllocation = EntityRestrictionCommonPredicates.applySecurityPredicate(svcAllocQ, dto, entityRestrictionService, serviceCategorisationService);
        // OR no security
        Predicate pNoServiceAllocation = groupActivity.serviceAllocation.isNull();

        // activity criteria
        BooleanExpression pService = dto.getServiceId() != null
                ? svcAllocQ.service.id.eq(dto.getServiceId())
                : null;
        BooleanExpression pSvcCat = dto.getServiceCategorisationId() != null
                ? svcAllocQ.id.eq(dto.getServiceCategorisationId())
                : null;
        BooleanExpression pVenue = dto.getVenueId() != null
                ? QGroupSupportActivity.groupSupportActivity.venue.id.eq(dto.getVenueId())
                : null;
        BooleanExpression pActivity = dto.getActivityTypeId() != null
                ? QGroupSupportActivity.groupSupportActivity.groupSupportActivityType.id.eq(dto.getActivityTypeId())
                : null;
        /*
        BooleanExpression pActivity = PredicateSupport.applyLocalDateRange(null,
                groupActivity.fromDate,
                dto.getFromDate(),
                dto.getToDate());
        */
        BooleanExpression pParent = dto.getParentId() != null
                ? QGroupSupportActivity.groupSupportActivity.parentId.eq(dto.getParentId())
                : null;

        BooleanExpression pType = dto.getGroupPageType().equals("sessions")
                ? QGroupSupportActivity.groupSupportActivity.course.isFalse()
                : QGroupSupportActivity.groupSupportActivity.course.isTrue();

        BooleanBuilder p = new BooleanBuilder();
        p.and(pParent).and(pType);
        p.and(pService).and(pActivity).and(pVenue);
        BooleanBuilder svcs = new BooleanBuilder();
        svcs.and(pServiceAllocation).or(pNoServiceAllocation);
        p.and(svcs);
        if (StringUtils.isNotBlank(type)) {
            p.and(QGroupSupportActivity.groupSupportActivity.discriminator_orm.eq(type));
        }
/*
        // q.limit(pr.getPageSize()).offset(pr.getOffset());
        //ReferralRepository.REFERRAL_SUMMARY_PROJECTION
        var projection =
                new QGroupSupportActivitySummary(
                        groupSupportActivity.id,
                        groupSupportActivity.uuid,
                        groupSupportActivity.description,
                        groupSupportActivity.fromDate,
                        groupSupportActivity.capacity,
                        groupSupportActivity.minutes,
                        groupSupportActivity.groupSupportActivityType,
                        groupSupportActivity.venue,
                        groupSupportActivity.venue.name,
                        groupSupportActivity.serviceAllocation.serviceId,
                        groupSupportActivity.serviceAllocation.projectId,
                        0,
                        0,
                        groupSupportActivity.toDate);
*/

        //Page<GroupSupportActivity> results = activityRepository.findAllWithProjection(QGroupSupportActivitySummary, p, pr);

        JPQLQuery<GroupSupportActivity> query = new JPAQuery<>(em);
        query.from(groupActivity)
            .leftJoin(groupActivity.serviceAllocation, svcAllocQ);
        query
            .where(p)
            .orderBy(groupActivity.fromDate.desc());
        var total = query.fetchCount();

        query.limit(pr.getPageSize()).offset(pr.getOffset());
        var activities = query.fetch();

        return new PageImpl(activities, pr, total);
    }

    public JPQLQuery<EvidenceSupportAction> supportActionInstanceSnapshotQuery(int serviceRecipientId,
                                                                               List<EvidenceGroup> evidenceGroups,
                                                                               DateTime workDate,
                                                                               DateTime asAt,
                                                                               boolean useAclSecurity) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        // convert from UTC/Instant to Local - which is required for the ReportCriteriaDto currently
        LocalDate local = workDate.withZone(DateTimeZone.forID("Europe/London")).toLocalDate();
        dto.setTo(local.toString(ISODateTimeFormat.date())); // local date - converted to UTC in PredicateSupport and +1

        // add a serviceRecipient filter - else we get the latest support plan for all srId's!!
        // we need a groupby for the srId otherwise this is handy, although best to limit to set of srIds I guess
        dto.setServiceRecipientFilter("serviceRecipient:" + serviceRecipientId);

        return supportActionInstanceSnapshotQuery(dto, asAt, evidenceGroups, true, useAclSecurity);
    }

    /**
     * Gets the actionInstances at a point in time, as per the questionnaire movement report.
     * The point in time is the work date, and the 'actionInstances' represent all the smart step data at that time.
     * Its important not to delve into the smartsteps without reference to the work, because its a temporal-based table
     * and so asking for the 'latest target date' may have since been edited to not be the latest.
     *
     * The actual query now has NO CROSS JOIN on services_projects, and select's and where clauses are correct in the subqueries
     * QUERY: The query is a select where workDate < today
     *      followed by a clause to select the max of an independent select for workDate's
     *      followed by a clause to select the max of an independent select for created within the same workDate
     * select generictyp0_.serviceRecipientId as col_0_0_, generictyp0_.id as col_1_0_, generictyp0_.id as id1_160_, generictyp0_.version as version2_160_, generictyp0_.created as created3_160_, generictyp0_.serviceRecipientId as serviceR4_160_, generictyp0_.workDate as workDate5_160_, generictyp0_.actionId as actionI15_160_, generictyp0_.activity as activity6_160_, generictyp0_.contactId as contact16_160_, generictyp0_.expiryDate as expiryDa7_160_, generictyp0_.goalName as goalName8_160_, generictyp0_.goalPlan as goalPlan9_160_, generictyp0_.score as score10_160_, generictyp0_.status as status11_160_, generictyp0_.statusChange as statusC12_160_, generictyp0_.statusChangeReasonId as statusC17_160_, generictyp0_.target as target13_160_, generictyp0_.actionInstanceUuid as actionI14_160_, generictyp0_.workUuid as workUui18_160_ from supportplanactions generictyp0_
     * inner join supportplanwork generictyp1_ on generictyp0_.workUuid=generictyp1_.uuid
     * inner join servicerecipients baseservic2_ on generictyp1_.serviceRecipientId=baseservic2_.id
     * inner join services_projects servicecat3_ on baseservic2_.serviceAllocationId=servicecat3_.id
     * inner join servicerecipients baseservic4_ on generictyp1_.serviceRecipientId=baseservic4_.id
     * inner join referrals referral5_ on baseservic4_.id=referral5_.serviceRecipientId where
     * (servicecat3_.serviceId in
     * (? , ? , ? , ?
     * )
     * )
     * and generictyp1_.workDate<''
     * and generictyp1_.evidenceGroupId=?
     * and generictyp1_.serviceRecipientId=?
     * and generictyp0_.workDate>=all
     * (select generictyp6_.workDate from supportplanactions generictyp6_
     * inner join supportplanwork generictyp7_ on generictyp6_.workUuid=generictyp7_.uuid
     * inner join servicerecipients baseservic8_ on generictyp7_.serviceRecipientId=baseservic8_.id
     * inner join services_projects servicecat9_ on baseservic8_.serviceAllocationId=servicecat9_.id
     * inner join servicerecipients baseservic10_ on generictyp7_.serviceRecipientId=baseservic10_.id
     * inner join referrals referral11_ on baseservic10_.id=referral11_.serviceRecipientId where generictyp6_.actionInstanceUuid=generictyp0_.actionInstanceUuid
     * and
     * (servicecat9_.serviceId in
     * (? , ? , ? , ?
     * )
     * )
     * and generictyp7_.workDate<''
     * and generictyp7_.evidenceGroupId=?
     * and generictyp7_.serviceRecipientId=?
     * and generictyp6_.serviceRecipientId=generictyp0_.serviceRecipientId
     * )
     * and generictyp0_.created>=all
     * (select generictyp12_.created from supportplanactions generictyp12_
     * inner join supportplanwork generictyp13_ on generictyp12_.workUuid=generictyp13_.uuid
     * inner join servicerecipients baseservic14_ on generictyp13_.serviceRecipientId=baseservic14_.id
     * inner join services_projects servicecat15_ on baseservic14_.serviceAllocationId=servicecat15_.id
     * inner join servicerecipients baseservic16_ on generictyp13_.serviceRecipientId=baseservic16_.id
     * inner join referrals referral17_ on baseservic16_.id=referral17_.serviceRecipientId where generictyp12_.actionInstanceUuid=generictyp0_.actionInstanceUuid
     * and
     * (servicecat15_.serviceId in
     * (? , ? , ? , ?
     * )
     * )
     * and generictyp13_.workDate<''
     * and generictyp13_.evidenceGroupId=?
     * and generictyp13_.serviceRecipientId=?
     * and generictyp12_.serviceRecipientId=generictyp0_.serviceRecipientId
     * and generictyp12_.workDate=generictyp0_.workDate
     * and generictyp12_.created<=?
     * )
     */
    public JPQLQuery<EvidenceSupportAction> supportActionInstanceSnapshotQuery(ReportCriteriaDto dto,
                                                                               List<EvidenceGroup> evidenceGroups,
                                                                               boolean latest,
                                                                               boolean useAclSecurity) {
        return supportActionInstanceSnapshotQuery(dto, null, evidenceGroups, latest, useAclSecurity);
    }
    public JPQLQuery<EvidenceSupportAction> supportActionInstanceSnapshotQuery(ReportCriteriaDto dto,
                                                                               DateTime created,
                                                                               List<EvidenceGroup> evidenceGroups,
                                                                               boolean latest,
                                                                               boolean useAclSecurity) {
        QEvidenceSupportWork workAlias = QEvidenceSupportWork.evidenceSupportWork;
        QEvidenceSupportAction itemAlias = QEvidenceSupportAction.evidenceSupportAction;
        QEvidenceSupportAction subQueryItemAlias = new QEvidenceSupportAction("subItemAlias");
        Predicate subQueryItemIdPredicate = subQueryItemAlias.actionInstanceUuid.eq(itemAlias.actionInstanceUuid);
        QEvidenceSupportWork subQueryWorkAlias = new QEvidenceSupportWork("subWorkAlias");

        JPQLQuery<EvidenceSupportAction> itemToWorkSubQueryWorkDate = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest actionInstanceUuid for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPQLQuery<EvidenceSupportAction> itemToWorkSubQueryCreated = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest questionId for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPQLQuery<EvidenceSupportAction> itemToWorkQuery = new JPAQuery<EvidenceSupportAction>(em)
                .from(itemAlias)
                // inner join here so that we use the same alias for predicates applied outside this method
                .innerJoin(itemAlias.work, workAlias);

        if ("targetDate".equals(dto.getSelectionPropertyPath())) {
            // we want those latest work items where the latest targetDate is < to
            // 'snapshotQuery' goes on to get 'latest' smart steps of all work upto now
            itemToWorkQuery = itemToWorkQuery.where(itemAlias.target.loe(dto.getToDate().plusDays(1).toDateTimeAtStartOfDay()));
        }

        return snapshotQuery(ReportController.qGTSW, subQueryItemAlias._super._super, subQueryWorkAlias._super._super, itemAlias._super._super,
                itemToWorkSubQueryWorkDate, itemToWorkSubQueryCreated, itemToWorkQuery, dto, evidenceGroups, latest, created, useAclSecurity);
    }


    // NB CLONE of supportActionInstanceSnapshotQuery, but query-dsl is not easy to work with an abstract .work
    // which is required to make it work with the dependents (ie GenercTypeSupportAction)
    public JPQLQuery<EvidenceThreatAction> threatActionInstanceSnapshotQuery(int serviceRecipientId,
                                                                             List<EvidenceGroup> evidenceGroups,
                                                                             DateTime workDate,
                                                                             DateTime asAt,
                                                                             boolean useAclSecurity) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        // convert from UTC/Instant to Local - which is required for the ReportCriteriaDto currently
        LocalDate local = workDate.withZone(DateTimeZone.forID("Europe/London")).toLocalDate();
        dto.setTo(local.toString(ISODateTimeFormat.date())); // local date - converted to UTC in PredicateSupport and +1

        // add a serviceRecipient filter - else we get the latest support plan for all srId's!!
        // we need a groupby for the srId otherwise this is handy, although best to limit to set of srIds I guess
        dto.setServiceRecipientFilter("serviceRecipient:" + serviceRecipientId);

        return threatActionInstanceSnapshotQuery(dto, asAt, evidenceGroups, true, useAclSecurity);
    }

    public JPQLQuery<EvidenceThreatAction> threatActionInstanceSnapshotQuery(ReportCriteriaDto dto,
                                                                             List<EvidenceGroup> evidenceGroups,
                                                                             boolean latest,
                                                                             boolean useAclSecurity) {
        return threatActionInstanceSnapshotQuery(dto, null, evidenceGroups, latest, useAclSecurity);
    }
    public JPQLQuery<EvidenceThreatAction> threatActionInstanceSnapshotQuery(ReportCriteriaDto dto,
                                                                             DateTime created,
                                                                             List<EvidenceGroup> evidenceGroups,
                                                                             boolean latest,
                                                                             boolean useAclSecurity) {
        QEvidenceThreatWork workAlias = QEvidenceThreatWork.evidenceThreatWork;
        QEvidenceThreatAction itemAlias = QEvidenceThreatAction.evidenceThreatAction;
        QEvidenceThreatAction subQueryItemAlias = new QEvidenceThreatAction("subItemAlias");
        Predicate subQueryItemIdPredicate = subQueryItemAlias.actionInstanceUuid.eq(itemAlias.actionInstanceUuid);
        QEvidenceThreatWork subQueryWorkAlias = new QEvidenceThreatWork("subWorkAlias");

        JPQLQuery<EvidenceThreatAction> itemToWorkSubQueryWorkDate = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest actionInstanceUuid for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPQLQuery<EvidenceThreatAction> itemToWorkSubQueryCreated = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest questionId for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPQLQuery<EvidenceThreatAction> itemToWorkQuery = new JPAQuery<EvidenceThreatAction>(em)
                .from(itemAlias)
                // inner join here so that we use the same alias for predicates applied outside this method
                .innerJoin(itemAlias.work, workAlias);

        if ("targetDate".equals(dto.getSelectionPropertyPath())) {
            // we want those latest work items where the latest targetDate is < to
            // 'snapshotQuery' goes on to get 'latest' smart steps of all work upto now
            itemToWorkQuery = itemToWorkQuery.where(itemAlias.target.loe(dto.getToDate().plusDays(1).toDateTimeAtStartOfDay()));
        }

        return snapshotQuery(ReportController.qGTTW, subQueryItemAlias._super._super, subQueryWorkAlias._super._super, itemAlias._super._super,
                itemToWorkSubQueryWorkDate, itemToWorkSubQueryCreated, itemToWorkQuery, dto, evidenceGroups, latest, created, useAclSecurity);
    }

    public JPQLQuery<EvidenceSupportAnswer> supportAnswerInstanceSnapshotQuery(int serviceRecipientId,
                                                                               List<EvidenceGroup> evidenceGroups,
                                                                               DateTime workDate,
                                                                               DateTime asAt,
                                                                               boolean useAclSecurity) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        // convert from UTC/Instant to Local - which is required for the ReportCriteriaDto currently
        LocalDate local = workDate.withZone(DateTimeZone.forID("Europe/London")).toLocalDate();
        dto.setTo(local.toString(ISODateTimeFormat.date())); // local date - converted to UTC in PredicateSupport and +1

        // add a serviceRecipient filter - else we get the latest support plan for all srId's!!
        // we need a groupby for the srId otherwise this is handy, although best to limit to set of srIds I guess
        dto.setServiceRecipientFilter("serviceRecipient:" + serviceRecipientId);

        return supportAnswerInstanceSnapshotQuery(dto, asAt, evidenceGroups, true, useAclSecurity);
    }

    public JPQLQuery<EvidenceSupportAnswer> supportAnswerInstanceSnapshotQuery(ReportCriteriaDto dto,
                                                                               List<EvidenceGroup> evidenceGroups,
                                                                               boolean latest,
                                                                               boolean useAclSecurity) {
        return supportAnswerInstanceSnapshotQuery(dto, null, evidenceGroups, latest, useAclSecurity);
    }
    public JPQLQuery<EvidenceSupportAnswer> supportAnswerInstanceSnapshotQuery(ReportCriteriaDto dto,
                                                                               DateTime created,
                                                                               List<EvidenceGroup> evidenceGroups,
                                                                               boolean latest,
                                                                               boolean useAclSecurity) {
        QEvidenceSupportWork workAlias = QEvidenceSupportWork.evidenceSupportWork;
        QEvidenceSupportAnswer itemAlias = QEvidenceSupportAnswer.evidenceSupportAnswer;
        QEvidenceSupportAnswer subQueryItemAlias = new QEvidenceSupportAnswer("subItemAlias");
        Predicate subQueryItemIdPredicate = subQueryItemAlias.question.id.eq(itemAlias.question.id);
        QEvidenceSupportWork subQueryWorkAlias = new QEvidenceSupportWork("subWorkAlias");

        JPQLQuery<EvidenceSupportAnswer> itemToWorkSubQueryWorkDate = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest actionInstanceUuid for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPQLQuery<EvidenceSupportAnswer> itemToWorkSubQueryCreated = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest questionId for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPQLQuery<EvidenceSupportAnswer> itemToWorkQuery = new JPAQuery<EvidenceSupportAnswer>(em)
                .from(itemAlias)
                // inner join here so that we use the same alias for predicates applied outside this method
                .innerJoin(itemAlias.work, workAlias);

        return snapshotQuery(ReportController.qGTSW, subQueryItemAlias._super._super, subQueryWorkAlias._super._super, itemAlias._super._super,
                itemToWorkSubQueryWorkDate, itemToWorkSubQueryCreated, itemToWorkQuery, dto, evidenceGroups, latest, created, useAclSecurity);
    }

    /**
     * The generic snapshot query capable of returning a snapshot at a workdate+created moment in time.
     * Whilst this can be replaced by getting a 'top1' query against each actionInstanceUuid (based on workDate desc, created asc)
     * which would look simpler, this query has the capability of querying across all srId's - as its used by the questionnaire
     * movement report, and now smart step reports, and should be used to present historical data when editing.
     */
    <T extends BaseEvidence> JPQLQuery<T> snapshotQuery(QAbstractBaseWorkEvidence gtsw,
                                                        QBaseEvidence subQueryItemAlias,
                                                        QAbstractBaseWorkEvidence subQueryWorkAlias,
                                                        QBaseEvidence queryItemAlias,
                                                        JPQLQuery<T> itemToWorkSubQueryWorkDate,
                                                        JPQLQuery<T> itemToWorkSubQueryCreated,
                                                        JPQLQuery<T> itemToWorkQuery,
                                                        ReportCriteriaDto dto, List<EvidenceGroup> evidenceGroups, boolean latest,
                                                        DateTime createdAsAt,
                                                        boolean useAclSecurity) {

        /*
         * Predicate for security and date ranges of work - for the outer query and the sub query.
         */

        // the predicate and queryModifier go hand-in-hand really since the queryModifier
        // applies an inner join on serviceRecipient (without it the query uses a cross join - which is all rows)
        QServiceCategorisation qSvcCat = QServiceCategorisation.serviceCategorisation;
        Predicate workPredicate = evidencePredicate(gtsw, qSvcCat, dto, evidenceGroups, null, true, false, useAclSecurity); // applies work dates

        QServiceCategorisation subQuerySvcCatAlias = new QServiceCategorisation("subSvcCatAlias");
        Predicate subQueryWorkPredicate = evidencePredicate(subQueryWorkAlias, subQuerySvcCatAlias, dto, evidenceGroups, null, true, false, useAclSecurity); // with work dates

        /*
         * Prepare aliases so we get inner joins, not cross joins
         */

        QReferral subQueryReferralAlias = new QReferral("subReferralAlias");
        QReferralServiceRecipient subQueryRefSvcAlias = new QReferralServiceRecipient("subServiceRecipientAlias");
        // join subQueryWorkAlias -> subQueryServiceRecipientAlias -> subQueryReferralAlias
        QueryModifier<T> subQueryModifier = applyBaseServiceRecipientToReferralCriteriaModifier(dto, subQueryReferralAlias, subQueryRefSvcAlias, subQueryWorkAlias.serviceRecipient);

        /*
         * Add predicate to the sub-queries.
         *
         * We need this on the sub-query since its not joined to the outer query directly, so we can select >= based on a criteria.
         */

        // this JPASubQuery is a clone of the below JPAQuery so that the sub query has the same criteria as the main query
        // so that we are looking for the latest/earliest within the criteria of serviceId, evidenceGroupId, workDate ranges etc
        itemToWorkSubQueryWorkDate
                // prevent a cross join to serviceAlloction
                .innerJoin(subQueryWorkAlias.serviceRecipient.serviceAllocation, subQuerySvcCatAlias)
                // predicate requires the use of the queryModifier to reduce a cross join to an inner join
                .where(subQueryWorkPredicate,
                       subQueryItemAlias.serviceRecipientId.eq(queryItemAlias.serviceRecipientId)
                );
        itemToWorkSubQueryCreated
                // prevent a cross join to serviceAlloction
                .innerJoin(subQueryWorkAlias.serviceRecipient.serviceAllocation, subQuerySvcCatAlias)
                // predicate requires the use of the queryModifier to reduce a cross join to an inner join
                .where(subQueryWorkPredicate,
                        subQueryItemAlias.serviceRecipientId.eq(queryItemAlias.serviceRecipientId)
                );
        JPQLQuery<T> itemToWorkWorkDateSubQuery = subQueryModifier.apply(itemToWorkSubQueryWorkDate);
        JPQLQuery<T> itemToWorkCreatedSubQuery = subQueryModifier.apply(itemToWorkSubQueryCreated);

        /*
         * Create the sub query based on created.
         *
         * Some data has duplicate answers for workDate, questionId and serviceRecipientId but with a different
         * workUuid and created date because they re-entered the details in order to 'correct' the first data.
         * So without this clause the results include all the answers - resulting in movement that isn't
         * actually movement (observed in the breakdown table where data is of the same snapshot period).
         * So we further restrict to the latest created date with the same workDate, questionId, servicerecipientId.
         *
         * This is also the location we can restrict the created date - thereby getting the snapshot at a workdate
         * and created combination - which is what is required for an accurate snapshot.
         */
        // TODO we need to remove action.workDate as its not updated when editing a work item (probably the problem above)
        itemToWorkCreatedSubQuery
                // further limit be the same workDate... and questionId, and serviceRecipientId, and predicate...
                .where(subQueryItemAlias.workDate.eq(queryItemAlias.workDate));

        // further limit up to the snapshot created date
        if (createdAsAt != null) {
            itemToWorkCreatedSubQuery.where(subQueryItemAlias.created.loe(createdAsAt));
        }


        /*
         * Predicate for main query.
         */

        // link the query to the subquery
        BooleanExpression latestOrEarliestWork = latest
                ? queryItemAlias.workDate.goeAll(itemToWorkWorkDateSubQuery.select(subQueryItemAlias.workDate))
                : queryItemAlias.workDate.loeAll(itemToWorkWorkDateSubQuery.select(subQueryItemAlias.workDate));
        // even 'earliest work' uses the later created date - since any previous value on the same workDate was surely a mistake
        BooleanExpression latestOrEarliestWorkAndCreated = latestOrEarliestWork.and(
                // this ensures we only get one answer UNLESS there is a duplicate created - which is 'to the second'
                queryItemAlias.created.goeAll(itemToWorkCreatedSubQuery.select(subQueryItemAlias.created)));

        /*
         * Add predicate to the query.
         */

        JPQLQuery<T> baseQuery = itemToWorkQuery
                // prevent a cross join to serviceAlloction
                .innerJoin(gtsw.serviceRecipient, QBaseServiceRecipient.baseServiceRecipient)
                .innerJoin(QBaseServiceRecipient.baseServiceRecipient.serviceAllocation, qSvcCat)
                // predicate requires the use of the queryModifier to reduce a cross join to an inner join
                .where(workPredicate, latestOrEarliestWorkAndCreated);

        // Prepare aliases so we get inner joins, not cross joins
        QueryModifier<T>  queryModifier = applyBaseServiceRecipientToReferralCriteriaModifier(dto, QReferral.referral, QReferralServiceRecipient.referralServiceRecipient, gtsw.serviceRecipient);
        return queryModifier.apply(baseQuery);
    }

    public <R extends AbstractBaseWorkEvidence, U extends EntityPathBase<R>> JPQLQuery<R> workSnapshotQuery(QAbstractBaseWorkEvidence queryWorkAlias,
                                          U workAlias,
                                          U subQueryWorkAlias,
                                          QAbstractBaseWorkEvidence subQueryWorkAliasSuper,
                                          ReportCriteriaDto dto,
                                          List<EvidenceGroup> evidenceGroups,
                                          boolean latest,
                                          boolean useAclSecurity) {
        return workSnapshotQuery(queryWorkAlias, workAlias, subQueryWorkAlias, subQueryWorkAliasSuper, dto, null, evidenceGroups, latest, useAclSecurity);
    }
    public <R extends AbstractBaseWorkEvidence, U extends EntityPathBase<R>> JPQLQuery<R> workSnapshotQuery(QAbstractBaseWorkEvidence queryWorkAlias,
                                          U workAlias,
                                          U subQueryWorkAlias,
                                          QAbstractBaseWorkEvidence subQueryWorkAliasSuper,
                                          ReportCriteriaDto dto, DateTime created,
                                          List<EvidenceGroup> evidenceGroups,
                                          boolean latest,
                                          boolean useAclSecurity) {
        JPQLQuery<R> workSubQueryWorkDate = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryWorkAlias);

        var workSubQueryCreated = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryWorkAlias);

        var workToWorkQuery = new JPAQuery<R>(em)
                .from(workAlias);

        return workSnapshotQuery(queryWorkAlias, subQueryWorkAliasSuper,
                workSubQueryWorkDate, workSubQueryCreated, workToWorkQuery, dto, evidenceGroups, latest, created, useAclSecurity);
    }

    /**
     * CLONE of snapshotQuery, just with QAbstractBaseWorkEvidence in parameters and return
     */
    <R extends BaseWorkEvidence> JPQLQuery<R> workSnapshotQuery(QAbstractBaseWorkEvidence queryWorkAlias,
                                                                QAbstractBaseWorkEvidence subQueryWorkAlias,
                                                                JPQLQuery<R> workSubQueryWorkDate,
                                                                JPQLQuery<R> workSubQueryCreated,
                                                                JPQLQuery<R> workToWorkQuery,
                                                                ReportCriteriaDto dto, List<EvidenceGroup> evidenceGroups, boolean latest,
                                                                DateTime createdAsAt,
                                                                boolean useAclSecurity) {

        /*
         * Predicate for security and date ranges of work - for the outer query and the sub query.
         */

        // the predicate and queryModifier go hand-in-hand really since the queryModifier
        // applies an inner join on serviceRecipient (without it the query uses a cross join - which is all rows)
        QServiceCategorisation qSvcCat = QServiceCategorisation.serviceCategorisation;
        Predicate workPredicate = evidencePredicate(queryWorkAlias, qSvcCat, dto, evidenceGroups, null, true, false, useAclSecurity); // applies work dates

        QServiceCategorisation subQuerySvcCatAlias = new QServiceCategorisation("subSvcCatAlias");
        Predicate subQueryWorkPredicate = evidencePredicate(subQueryWorkAlias, subQuerySvcCatAlias, dto, evidenceGroups, null, true, false, useAclSecurity); // with work dates

        /*
         * Prepare aliases so we get inner joins, not cross joins
         */

        QReferral subQueryReferralAlias = new QReferral("subReferralAlias");
        QReferralServiceRecipient subQueryRefSvcAlias = new QReferralServiceRecipient("subServiceRecipientAlias");
        // join subQueryWorkAlias -> subQueryServiceRecipientAlias -> subQueryReferralAlias
        QueryModifier<R> subQueryModifier = applyBaseServiceRecipientToReferralCriteriaModifier(dto, subQueryReferralAlias, subQueryRefSvcAlias, subQueryWorkAlias.serviceRecipient);

        /*
         * Add predicate to the sub-queries.
         *
         * We need this on the sub-query since its not joined to the outer query directly, so we can select >= based on a criteria.
         */

        // this JPASubQuery is a clone of the below JPAQuery so that the sub query has the same criteria as the main query
        // so that we are looking for the latest/earliest within the criteria of serviceId, evidenceGroupId, workDate ranges etc
        workSubQueryWorkDate
                // prevent a cross join to serviceAlloction
                .innerJoin(subQueryWorkAlias.serviceRecipient.serviceAllocation, subQuerySvcCatAlias)
                // predicate requires the use of the queryModifier to reduce a cross join to an inner join
                .where(subQueryWorkPredicate,
                        subQueryWorkAlias.serviceRecipientId.eq(queryWorkAlias.serviceRecipientId),
                        subQueryWorkAlias.taskDefId.eq(queryWorkAlias.taskDefId)
                );
        workSubQueryCreated
                // prevent a cross join to serviceAlloction
                .innerJoin(subQueryWorkAlias.serviceRecipient.serviceAllocation, subQuerySvcCatAlias)
                // predicate requires the use of the queryModifier to reduce a cross join to an inner join
                .where(subQueryWorkPredicate,
                        subQueryWorkAlias.serviceRecipientId.eq(queryWorkAlias.serviceRecipientId),
                        subQueryWorkAlias.taskDefId.eq(queryWorkAlias.taskDefId)
                );
        JPQLQuery<R> workDateSubQuery = subQueryModifier.apply(workSubQueryWorkDate);
        JPQLQuery<R> createdSubQuery = subQueryModifier.apply(workSubQueryCreated);

        /*
         * Create the sub query based on created.
         *
         * Some data has duplicate answers for workDate, questionId and serviceRecipientId but with a different
         * workUuid and created date because they re-entered the details in order to 'correct' the first data.
         * So without this clause the results include all the answers - resulting in movement that isn't
         * actually movement (observed in the breakdown table where data is of the same snapshot period).
         * So we further restrict to the latest created date with the same workDate, questionId, servicerecipientId.
         *
         * This is also the location we can restrict the created date - thereby getting the snapshot at a workdate
         * and created combination - which is what is required for an accurate snapshot.
         */
        createdSubQuery
                // further limit be the same workDate... and questionId, and serviceRecipientId, and predicate...
                .where(subQueryWorkAlias.workDate.eq(queryWorkAlias.workDate));

        // further limit up to the snapshot created date
        if (createdAsAt != null) {
            createdSubQuery.where(subQueryWorkAlias.created.loe(createdAsAt));
        }


        /*
         * Predicate for main query.
         */

        // link the query to the subquery
        BooleanExpression latestOrEarliestWork = latest
                ? queryWorkAlias.workDate.goeAll(workDateSubQuery.select(subQueryWorkAlias.workDate))
                : queryWorkAlias.workDate.loeAll(workDateSubQuery.select(subQueryWorkAlias.workDate));
        // even 'earliest work' uses the later created date - since any previous value on the same workDate was surely a mistake
        BooleanExpression latestOrEarliestWorkAndCreated = latestOrEarliestWork.and(
                // this ensures we only get one answer UNLESS there is a duplicate created - which is 'to the second'
                queryWorkAlias.created.goeAll(createdSubQuery.select(subQueryWorkAlias.created)));

        /*
         * Add predicate to the query.
         */

        JPQLQuery<R> baseQuery = workToWorkQuery
                // prevent a cross join to serviceAlloction
                .innerJoin(queryWorkAlias.serviceRecipient, QBaseServiceRecipient.baseServiceRecipient)
                .innerJoin(QBaseServiceRecipient.baseServiceRecipient.serviceAllocation, qSvcCat)
                // predicate requires the use of the queryModifier to reduce a cross join to an inner join
                .where(workPredicate, latestOrEarliestWorkAndCreated);

        // Prepare aliases so we get inner joins, not cross joins
        QueryModifier<R>  queryModifier = applyBaseServiceRecipientToReferralCriteriaModifier(dto, QReferral.referral, QReferralServiceRecipient.referralServiceRecipient, queryWorkAlias.serviceRecipient);
        return queryModifier.apply(baseQuery);
    }

    /**
     * Execute the query and convert into a map of srId, actionInstanceUuid
     * See ReportController.getQuestionnaireAnswersSnapshotViewModels
     */
    public List<SupportSmartStepsSnapshotViewModel> getSupportActionInstanceSnapshotViewModels(
            QEvidenceSupportAction itemAlias, EvidenceGroup evidenceGroup, JPQLQuery<EvidenceSupportAction> query) {
        // execute the query, using a transformation
        // transform the result into a map of <srId, actionInstances>
        // NB this is done in memory, and paging is applied on the query not srId boundaries
        // TODO ensure the consumer knows to append subsequent paged answers since the key may already exist
        Map<Integer, List<EvidenceSupportAction>> actions = query
                .transform(
                        groupBy(itemAlias.serviceRecipientId)
                                .as(list(itemAlias))
                );

        // extract the action into the existing data structure
        // although it may be better to use query-dsl, but it appears this is also in-memory
        // see 'Result aggregation' and 'Result post-processing'
        return actions.entrySet().stream()
                .map(entry -> {
                    SupportSmartStepsSnapshotViewModel result = new SupportSmartStepsSnapshotViewModel();
                    result.serviceRecipientId = entry.getKey();
                    result.evidenceGroupKey = evidenceGroup;
                    result.latestActions = entry.getValue().stream().map(supportActionsToViewModel).collect(toList());
                    result.parentId = referralRepository.getReferralIdByServiceRecipientId(result.serviceRecipientId); // TODO: This will be NULL for buildings - which is fine
                    return result;
                })
                .collect(toList());
    }

    // CLONE of above method
    public List<ThreatSmartStepsSnapshotViewModel> getThreatActionInstanceSnapshotViewModels(
            QEvidenceThreatAction itemAlias, EvidenceGroup evidenceGroup, JPQLQuery<EvidenceThreatAction> query) {
        // execute the query, using a transformation
        // transform the result into a map of <srId, actionInstances>
        // NB this is done in memory, and paging is applied on the query not srId boundaries
        // TODO ensure the consumer knows to append subsequent paged answers since the key may already exist
        Map<Integer, List<EvidenceThreatAction>> actions = query
                .transform(
                        groupBy(itemAlias.serviceRecipientId)
                                .as(list(itemAlias))
                );

        // extract the action into the existing data structure
        // although it may be better to use query-dsl, but it appears this is also in-memory
        // see 'Result aggregation' and 'Result post-processing'
        return actions.entrySet().stream()
                .map(entry -> {
                    ThreatSmartStepsSnapshotViewModel result = new ThreatSmartStepsSnapshotViewModel();
                    result.serviceRecipientId = entry.getKey();
                    result.evidenceGroupKey = evidenceGroup;
                    result.latestActions = entry.getValue().stream().map(threatActionsToViewModel).collect(toList());
                    result.parentId = referralRepository.getReferralIdByServiceRecipientId(result.serviceRecipientId); // TODO: This will be NULL for buildings - which is fine
                    return result;
                })
                .collect(toList());
    }

    // see ECCO-2240 Explicitly join the referral to the BaseServiceRecipient so that the criteria works at run-time
    // this is because we either need to move queried items to a base class (eg acceptOnService) or join to referrals
    public <T> QueryModifier<T> applyBaseServiceRecipientToReferralCriteriaModifier(ReportCriteriaDto dto,
                                                                                    QReferral ref,
                                                                                    QReferralServiceRecipient sr,
                                                                                    QBaseServiceRecipient srBase) {
        // no-op by default
        QueryModifier<T> queryModifier = jpqlQuery -> jpqlQuery;
        Predicate pReferral = ReferralPredicates.applyReferralReportCriteria(new BooleanBuilder(), ref, dto, dto.getFromDate(), dto.getToDate());

        // if we have some report criteria within ReportCriteriaDto, then we can assume to be testing a referral
        if (((BooleanBuilder) pReferral).hasValue()) {
            queryModifier = jpqlQuery -> jpqlQuery
                    .join(srBase.as(QReferralServiceRecipient.class), sr)
                    .join(sr.referral, ref)
                    .where(pReferral);
        }

        return queryModifier;
    }

    Predicate evidencePredicate(QAbstractBaseWorkEvidence gtsw, ReportCriteriaDto dto, @Nullable List<EvidenceGroup> evidenceGroupIds, TaskDefinition.@Nullable Type taskType,
                                boolean restrictEvidenceGroupId, boolean restrictTaskDefType, boolean useAclSecurity) {
        return evidencePredicate(gtsw, gtsw.serviceRecipient.serviceAllocation, dto, evidenceGroupIds, taskType, restrictEvidenceGroupId, restrictTaskDefType, useAclSecurity);
    }

    Predicate evidencePredicate(QAbstractBaseWorkEvidence gtsw, QServiceCategorisation svcCat, ReportCriteriaDto dto, @Nullable List<EvidenceGroup> evidenceGroupIds, TaskDefinition.@Nullable Type taskDefType,
                                boolean restrictEvidenceGroupId, boolean restrictTaskDefType, boolean useAclSecurity) {
        log.info("Running report for work criteria " + dto.toString());

        Predicate p = evidenceBasePredicate(gtsw, svcCat, dto, useAclSecurity);

        // currently only used in ReportController.allQuestionnaireWorkByCriteria
        if (restrictTaskDefType) { // qnrs
            // quick query to get the ids of the types - because we've always used the evidenceGroup as an existing taskDefId
            var taskDefTypesQ = em.createQuery("select td.id from TaskDefinition td where td.type=:type", Long.class);
            taskDefTypesQ.setParameter("type" , taskDefType);
            var evidenceGroupIdsOfType = taskDefTypesQ.getResultList();

            p = and(p, gtsw.evidenceGroupId.in(evidenceGroupIdsOfType));
        }
        if (restrictEvidenceGroupId && (evidenceGroupIds != null)) { // support
            var groupIds = evidenceGroupIds.stream().map(EvidenceGroup::getId).collect(Collectors.toList());
            p = and(p, gtsw.evidenceGroupId.in(groupIds));
        }

        return p;
    }

    Predicate evidenceBasePredicate(QAbstractBaseWorkEvidence bwe, QServiceCategorisation svcCat, ReportCriteriaDto dto, boolean useAclSecurity) {
        log.info("Running report for work criteria " + dto.toString());

        Predicate p = useAclSecurity
                ? EntityRestrictionCommonPredicates.applySecurityPredicate(svcCat, dto, entityRestrictionService, serviceCategorisationService)
                : null;

        // always apply the workDate
        p = PredicateSupport.applyLocalDateRange(p, bwe.workDate, dto.getFromDate(), dto.getToDate());

        if (dto.getTaskDefName() != null) {
            var task = taskDefinitionService.findOneByNameIgnoreCase(dto.getTaskDefName());
            p = and(p, bwe.taskDefId.eq(task.getId()));
        }

        if (dto.getServiceRecipientFilter() != null) {
            p = and(p, bwe.serviceRecipientId.eq(Integer.parseInt(StringUtils.substringAfter(dto.getServiceRecipientFilter(), ":"))));
        }

        return p;
    }

    public JPQLQuery<EvidenceSupportAnswer> questionnaireSnapshotQuery(int serviceRecipientId, List<EvidenceGroup> evidenceGroups, DateTime workDate, DateTime asAt, boolean useAclSecurity) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        // convert from UTC/Instant to Local - which is required for the ReportCriteriaDto currently
        LocalDate local = workDate.withZone(DateTimeZone.forID("Europe/London")).toLocalDate();
        dto.setTo(local.toString(ISODateTimeFormat.date())); // local date - converted to UTC in PredicateSupport and +1


        // add a serviceRecipient filter - else we get the latest support plan for all srId's!!
        dto.setServiceRecipientFilter("serviceRecipient:" + serviceRecipientId);

        return questionnaireSnapshotQuery(dto, asAt, evidenceGroups, true, useAclSecurity);
    }

    /**
     * The commit of this snapshop method produces:
     select *
     from ecco.supportplananswers generictyp0_
     inner join ecco.supportplanwork generictyp1_ on generictyp0_.workUuid=generictyp1_.uuid
     inner join ecco.servicerecipients baseservic2_ on generictyp1_.serviceRecipientId=baseservic2_.id
     inner join ecco.referrals referral3_ on baseservic2_.id=referral3_.serviceRecipientId
     where generictyp0_.workDate>=all
         (select generictyp5_.workDate from ecco.supportplananswers generictyp5_
         inner join ecco.supportplanwork generictyp6_ on generictyp5_.workUuid=generictyp6_.uuid
         inner join ecco.servicerecipients baseservic7_ on generictyp6_.serviceRecipientId=baseservic7_.id
         inner join ecco.referrals referral8_ on baseservic7_.id=referral8_.serviceRecipientId
         where generictyp5_.questionId=generictyp0_.questionId
         and generictyp5_.serviceRecipientId=generictyp0_.serviceRecipientId
         )
     and generictyp0_.created>=all
         (select generictyp10_.created from ecco.supportplananswers generictyp10_
         inner join ecco.supportplanwork generictyp11_ on generictyp10_.workUuid=generictyp11_.uuid
         inner join ecco.servicerecipients baseservic12_ on generictyp11_.serviceRecipientId=baseservic12_.id
         inner join ecco.referrals referral13_ on baseservic12_.id=referral13_.serviceRecipientId
         where generictyp10_.questionId=generictyp0_.questionId
         and generictyp10_.serviceRecipientId=generictyp0_.serviceRecipientId
         ** and generictyp10_.workDate=generictyp0_.workDate **
         )
     */
    // Perhaps make use of some generics - eg JPQLQuery<Referral> q = new JPAQuery<>(em).from(referral);
    // Perhaps instead of predicate, see if we could just send a path? NumberPath<Long> idPath = QQuestion.question.id;
    // Perhaps instead of direct queries, see if we could use generics, or send class - eg 'sw.serviceRecipient.as(QReferralServiceRecipient.class), sr'
    public JPQLQuery<EvidenceSupportAnswer> questionnaireSnapshotQuery(ReportCriteriaDto dto,
                                                                       List<EvidenceGroup> evidenceGroups,
                                                                       boolean latest,
                                                                       boolean useAclSecurity) {
        return questionnaireSnapshotQuery(dto, null, evidenceGroups, latest, useAclSecurity);
    }
    public JPQLQuery<EvidenceSupportAnswer> questionnaireSnapshotQuery(ReportCriteriaDto dto, DateTime created,
                                                                       List<EvidenceGroup> evidenceGroups,
                                                                       boolean latest,
                                                                       boolean useAclSecurity) {
        QEvidenceSupportWork workAlias = QEvidenceSupportWork.evidenceSupportWork;
        QEvidenceSupportAnswer itemAlias = QEvidenceSupportAnswer.evidenceSupportAnswer;
        QEvidenceSupportAnswer subQueryItemAlias = new QEvidenceSupportAnswer("itemAlias");
        Predicate subQueryItemIdPredicate = subQueryItemAlias.question.id.eq(itemAlias.question.id);
        QEvidenceSupportWork subQueryWorkAlias = new QEvidenceSupportWork("workAlias");

        JPQLQuery<EvidenceSupportAnswer> itemToWorkSubQueryWorkDate = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest questionId for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPQLQuery<EvidenceSupportAnswer> itemToWorkSubQueryCreated = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest questionId for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPAQuery<EvidenceSupportAnswer> itemToWorkQuery = new JPAQuery<EvidenceSupportAnswer>(em)
                .from(itemAlias)
                // inner join here so that we use the same alias for predicates applied outside this method
                .innerJoin(itemAlias.work, workAlias);

        return snapshotQuery(ReportController.qGTSW, subQueryItemAlias._super._super, subQueryWorkAlias._super._super, itemAlias._super._super,
                itemToWorkSubQueryWorkDate, itemToWorkSubQueryCreated, itemToWorkQuery, dto, evidenceGroups, latest, created, useAclSecurity);
    }

    /**
     * Execute the query and convert into a map of srId, answers
     */
    public List<QuestionnaireAnswersSnapshotViewModel> getQuestionnaireAnswersSnapshotViewModels(
            QEvidenceSupportAnswer itemAlias, EvidenceGroup evidenceGroup, JPQLQuery<EvidenceSupportAnswer> query) {
        // execute the query, using a transformation
        // transform the result into a map of <srId, answers>
        // NB this is done in memory, and paging is applied on the query not srId boundaries
        // TODO ensure the consumer knows to append subsequent paged answers since the key may already exist
        Map<Integer, List<EvidenceSupportAnswer>> answers = query
                .transform(
                        groupBy(itemAlias.serviceRecipientId)
                                .as(list(itemAlias))
                );

        // extract the answer into the existing data structure
        // although it may be better to use query-dsl, but it appears this is also in-memory
        // see 'Result aggregation' and 'Result post-processing'
        return answers.entrySet().stream()
                .map(entry -> {
                    QuestionnaireAnswersSnapshotViewModel vm = new QuestionnaireAnswersSnapshotViewModel();
                    vm.serviceRecipientId = entry.getKey();
                    vm.evidenceGroupKey = evidenceGroup;
                    vm.answers = entry.getValue().stream().map(answersToSnapshotViewModel).collect(toList());
                    return vm;
                })
                .collect(toList());
    }

    /**
     * FLAGS latest
     */
    public JPQLQuery<EvidenceSupportFlag> flagSnapshotQuery(int serviceRecipientId,
                                                            List<EvidenceGroup> evidenceGroups,
                                                            DateTime workDate,
                                                            DateTime asAt,
                                                            boolean useAclSecurity) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        // convert from UTC/Instant to Local - which is required for the ReportCriteriaDto currently
        LocalDate local = workDate.withZone(DateTimeZone.forID("Europe/London")).toLocalDate();
        dto.setTo(local.toString(ISODateTimeFormat.date())); // local date - converted to UTC in PredicateSupport and +1

        // add a serviceRecipient filter - else we get the latest support plan for all srId's!!
        dto.setServiceRecipientFilter("serviceRecipient:" + serviceRecipientId);

        return flagSnapshotQuery(dto, asAt, evidenceGroups, true, useAclSecurity);
    }
    public JPQLQuery<EvidenceSupportFlag> flagSnapshotQuery(ReportCriteriaDto dto,
                                                            List<EvidenceGroup> evidenceGroups,
                                                            boolean latest,
                                                            boolean useAclSecurity) {
        return flagSnapshotQuery(dto, null, evidenceGroups, latest, useAclSecurity);
    }
    public JPQLQuery<EvidenceSupportFlag> flagSnapshotQuery(ReportCriteriaDto dto, DateTime created,
                                                            List<EvidenceGroup> evidenceGroups,
                                                            boolean latest,
                                                            boolean useAclSecurity) {
        QEvidenceSupportWork workAlias = QEvidenceSupportWork.evidenceSupportWork;
        QEvidenceSupportFlag itemAlias = QEvidenceSupportFlag.evidenceSupportFlag;
        QEvidenceSupportFlag subQueryItemAlias = new QEvidenceSupportFlag("itemAlias");
        Predicate subQueryItemIdPredicate = subQueryItemAlias.flagDefId.eq(itemAlias.flagDefId);
        QEvidenceSupportWork subQueryWorkAlias = new QEvidenceSupportWork("workAlias");

        JPQLQuery<EvidenceSupportFlag> itemToWorkSubQueryWorkDate = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest questionId for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPQLQuery<EvidenceSupportFlag> itemToWorkSubQueryCreated = JPAExpressions
                // use an alias only - otherwise it generates a 'from table1,table2' cross join
                .selectFrom(subQueryItemAlias)
                // inner join here so that we use the same alias for the where
                .innerJoin(subQueryItemAlias.work, subQueryWorkAlias)
                // the unique criteria of the sub query - the latest questionId for each serviceRecipient
                .where(subQueryItemIdPredicate);

        JPAQuery<EvidenceSupportFlag> itemToWorkQuery = new JPAQuery<EvidenceSupportFlag>(em)
                .from(itemAlias)
                // inner join here so that we use the same alias for predicates applied outside this method
                .innerJoin(itemAlias.work, workAlias);

        return snapshotQuery(ReportController.qGTSW, subQueryItemAlias._super._super, subQueryWorkAlias._super._super, itemAlias._super._super,
                itemToWorkSubQueryWorkDate, itemToWorkSubQueryCreated, itemToWorkQuery, dto, evidenceGroups, latest, created, useAclSecurity);
    }

    public Setting getPageSize(SettingsService.SettingKey settingKey) {
        Setting pageSizeSetting = settingsService.settingFor(settingKey.namespace(), settingKey.key());
        return pageSizeSetting != null ?
                pageSizeSetting
                : settingsService.settingFor(PageSizeDefault);
    }

}